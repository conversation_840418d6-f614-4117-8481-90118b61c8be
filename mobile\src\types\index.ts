export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  budget: 'budget' | 'moderate' | 'luxury';
  interests: string[];
  accommodationType: 'hotel' | 'apartment' | 'hostel' | 'any';
  transportationType: 'flight' | 'train' | 'car' | 'bus' | 'any';
}

export interface Trip {
  id: string;
  userId: string;
  destination: string;
  title?: string;
  description?: string;
  startDate: string;
  endDate: string;
  duration: number;
  travelers: number;
  budget?: number;
  interests?: string[];
  travelStyle?: 'budget' | 'balanced' | 'luxury';
  accommodationType?: string;
  transportationType?: string;
  plan?: string;
  itinerary?: Itinerary[];
  preferences?: TripPreferences;
  status?: 'planning' | 'confirmed' | 'active' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

export interface TripPreferences {
  budget?: number;
  travelStyle: 'budget' | 'balanced' | 'luxury';
  interests: string[];
  accommodationType?: string;
  transportationType?: string;
}

export interface Itinerary {
  day: number;
  date: string;
  activities: Activity[];
}

export interface Activity {
  id: string;
  time: string;
  title: string;
  description?: string;
  location?: string;
  duration?: string;
  type: 'attraction' | 'restaurant' | 'activity' | 'transport' | 'accommodation';
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  price?: number;
  bookingRequired?: boolean;
}

export interface Accommodation {
  id: string;
  name: string;
  type: 'hotel' | 'apartment';
  location: string;
  price: number;
  rating: number;
  images: string[];
  amenities: string[];
  description: string;
  availableRooms?: number;
  maxGuests?: number;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface Attraction {
  id: string;
  name: string;
  location: string;
  category: string;
  rating: number;
  description: string;
  image: string;
  price: string;
  duration: string;
  bestTimeToVisit: string;
  tags: string[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface Restaurant {
  id: string;
  name: string;
  location: string;
  category: string;
  rating: number;
  description: string;
  image: string;
  priceRange: string;
  bestFor: string;
  tags: string[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface WeatherData {
  location: string;
  current: {
    temperature: number;
    condition: string;
    humidity: number;
    windSpeed: number;
  };
  forecast: WeatherForecast[];
}

export interface WeatherForecast {
  date: string;
  weather: string;
  temperature: {
    min: number;
    max: number;
  };
  precipitation: number;
  humidity: number;
}

export interface MapPoint {
  id: string;
  title: string;
  description: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  type: 'accommodation' | 'attraction' | 'restaurant' | 'activity';
  day?: number;
}

export interface Booking {
  id: string;
  userId: string;
  accommodationId: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'cancelled';
  createdAt: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: string;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  TripDetails: { tripId: string };
  CreateTrip: undefined;
  EditTrip: { tripId: string };
  TripMap: { tripId: string };
  Weather: { destination: string };
  AccommodationDetails: { accommodationId: string };
  BookingForm: { accommodation: Accommodation };
  MapView: { points: MapPoint[] };
  Profile: undefined;
  Settings: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Trips: undefined;
  Explore: undefined;
  Accommodations: undefined;
  Profile: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};
