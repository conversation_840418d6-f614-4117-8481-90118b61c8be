import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Appbar,
  Chip,
  HelperText,
  ActivityIndicator,
  RadioButton
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTrip } from '../../contexts/TripContext';
import { theme, spacing } from '../../constants/theme';

export default function CreateTripScreen({ navigation }: any) {
  const [destination, setDestination] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
  const [budget, setBudget] = useState('');
  const [travelers, setTravelers] = useState('1');
  const [interests, setInterests] = useState<string[]>([]);
  const [travelStyle, setTravelStyle] = useState('balanced');
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [loading, setLoading] = useState(false);

  const { createTrip } = useTrip();

  const availableInterests = [
    'Culture', 'Food', 'Adventure', 'Relaxation', 'Nature',
    'History', 'Shopping', 'Nightlife', 'Photography', 'Art'
  ];

  const travelStyles = [
    { value: 'budget', label: 'Budget-Friendly', description: 'Save money, maximize experiences' },
    { value: 'balanced', label: 'Balanced', description: 'Mix of comfort and value' },
    { value: 'luxury', label: 'Luxury', description: 'Premium experiences and comfort' }
  ];

  const toggleInterest = (interest: string) => {
    setInterests(prev =>
      prev.includes(interest)
        ? prev.filter(i => i !== interest)
        : [...prev, interest]
    );
  };

  const validateForm = () => {
    if (!destination.trim()) {
      Alert.alert('Error', 'Please enter a destination');
      return false;
    }

    if (startDate >= endDate) {
      Alert.alert('Error', 'End date must be after start date');
      return false;
    }

    if (parseInt(travelers) < 1 || parseInt(travelers) > 20) {
      Alert.alert('Error', 'Number of travelers must be between 1 and 20');
      return false;
    }

    return true;
  };

  const handleCreateTrip = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const tripData = {
        destination: destination.trim(),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        budget: budget ? parseFloat(budget) : undefined,
        travelers: parseInt(travelers),
        interests,
        travelStyle,
        preferences: {
          budget: budget ? parseFloat(budget) : undefined,
          travelStyle,
          interests
        }
      };

      await createTrip(tripData);
      Alert.alert(
        'Success',
        'Your trip is being planned! You will be notified when it\'s ready.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to create trip');
    } finally {
      setLoading(false);
    }
  };

  const onStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setStartDate(selectedDate);
      // Automatically adjust end date if it's before the new start date
      if (selectedDate >= endDate) {
        setEndDate(new Date(selectedDate.getTime() + 7 * 24 * 60 * 60 * 1000));
      }
    }
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Plan New Trip" />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Trip Details</Text>

            <TextInput
              label="Destination"
              value={destination}
              onChangeText={setDestination}
              mode="outlined"
              style={styles.input}
              placeholder="Where would you like to go?"
              left={<TextInput.Icon icon="map-marker" />}
            />

            <View style={styles.dateContainer}>
              <View style={styles.dateInput}>
                <Button
                  mode="outlined"
                  onPress={() => setShowStartDatePicker(true)}
                  style={styles.dateButton}
                  icon="calendar"
                >
                  Start: {startDate.toLocaleDateString()}
                </Button>
              </View>

              <View style={styles.dateInput}>
                <Button
                  mode="outlined"
                  onPress={() => setShowEndDatePicker(true)}
                  style={styles.dateButton}
                  icon="calendar"
                >
                  End: {endDate.toLocaleDateString()}
                </Button>
              </View>
            </View>

            <View style={styles.row}>
              <View style={styles.halfInput}>
                <TextInput
                  label="Travelers"
                  value={travelers}
                  onChangeText={setTravelers}
                  mode="outlined"
                  keyboardType="numeric"
                  left={<TextInput.Icon icon="account-group" />}
                />
              </View>

              <View style={styles.halfInput}>
                <TextInput
                  label="Budget (Optional)"
                  value={budget}
                  onChangeText={setBudget}
                  mode="outlined"
                  keyboardType="numeric"
                  placeholder="USD"
                  left={<TextInput.Icon icon="currency-usd" />}
                />
              </View>
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Travel Style</Text>
            <RadioButton.Group
              onValueChange={setTravelStyle}
              value={travelStyle}
            >
              {travelStyles.map((style) => (
                <View key={style.value} style={styles.radioItem}>
                  <RadioButton value={style.value} />
                  <View style={styles.radioContent}>
                    <Text style={styles.radioLabel}>{style.label}</Text>
                    <Text style={styles.radioDescription}>{style.description}</Text>
                  </View>
                </View>
              ))}
            </RadioButton.Group>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Interests</Text>
            <Text style={styles.sectionSubtitle}>
              Select what you're interested in (optional)
            </Text>

            <View style={styles.interestsContainer}>
              {availableInterests.map((interest) => (
                <Chip
                  key={interest}
                  selected={interests.includes(interest)}
                  onPress={() => toggleInterest(interest)}
                  style={styles.interestChip}
                  mode={interests.includes(interest) ? 'flat' : 'outlined'}
                >
                  {interest}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>

        <Button
          mode="contained"
          onPress={handleCreateTrip}
          loading={loading}
          disabled={loading}
          style={styles.createButton}
          labelStyle={styles.createButtonText}
        >
          {loading ? 'Creating Trip...' : 'Create Trip'}
        </Button>
      </ScrollView>

      {showStartDatePicker && (
        <DateTimePicker
          value={startDate}
          mode="date"
          display="default"
          onChange={onStartDateChange}
          minimumDate={new Date()}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={endDate}
          mode="date"
          display="default"
          onChange={onEndDateChange}
          minimumDate={startDate}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  card: {
    marginBottom: spacing.lg,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: theme.colors.primary,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: spacing.md,
    color: theme.colors.onSurfaceVariant,
  },
  input: {
    marginBottom: spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  dateInput: {
    flex: 1,
  },
  dateButton: {
    justifyContent: 'flex-start',
  },
  row: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  radioContent: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  radioLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  radioDescription: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  interestChip: {
    marginBottom: spacing.xs,
  },
  createButton: {
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
    paddingVertical: spacing.sm,
    borderRadius: 25,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
