import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Dimensions,
  Platform,
  SafeAreaView,
  StatusBar
} from 'react-native';

const { width, height } = Dimensions.get('window');
const isTablet = width > 768;
const isWeb = Platform.OS === 'web';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState('home');
  const [isLoading, setIsLoading] = useState(false);
  const [trips, setTrips] = useState([
    {
      id: 1,
      title: 'Tropical Paradise - Bali',
      destination: 'Bali, Indonesia',
      startDate: '2024-12-15',
      endDate: '2024-12-22',
      travelers: 2,
      budget: '$2,500',
      status: 'confirmed',
      image: '🏖️'
    },
    {
      id: 2,
      title: 'City of Lights - Paris',
      destination: 'Paris, France',
      startDate: '2025-01-10',
      endDate: '2025-01-17',
      travelers: 1,
      budget: '$3,000',
      status: 'planning',
      image: '🗼'
    }
  ]);
  const [tripData, setTripData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    travelers: '1',
    budget: '',
    preferences: ''
  });

  const handleCreateTrip = async () => {
    if (!tripData.destination) {
      Alert.alert('Error', 'Please enter a destination');
      return;
    }
    if (!tripData.startDate || !tripData.endDate) {
      Alert.alert('Error', 'Please enter start and end dates');
      return;
    }

    setIsLoading(true);

    // Simulate AI processing
    setTimeout(() => {
      const newTrip = {
        id: trips.length + 1,
        title: `Adventure in ${tripData.destination}`,
        destination: tripData.destination,
        startDate: tripData.startDate,
        endDate: tripData.endDate,
        travelers: parseInt(tripData.travelers),
        budget: tripData.budget || 'Not specified',
        status: 'planning',
        image: '✈️'
      };

      setTrips([...trips, newTrip]);
      setIsLoading(false);

      Alert.alert(
        'Success! 🎉',
        `AI Trip Plan for ${tripData.destination} created successfully!\n\n✅ Itinerary generated\n✅ Hotels found\n✅ Activities planned\n✅ Routes optimized`,
        [
          { text: 'View Trip', onPress: () => setCurrentScreen('trips') },
          { text: 'Create Another', onPress: () => {
            setTripData({ destination: '', startDate: '', endDate: '', travelers: '1', budget: '', preferences: '' });
          }},
          { text: 'Go Home', onPress: () => setCurrentScreen('home') }
        ]
      );
    }, 2000);
  };

  const renderHomeScreen = () => (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={[styles.title, isTablet && styles.titleTablet]}>🌍 AI Trip Planner</Text>
        <Text style={[styles.subtitle, isTablet && styles.subtitleTablet]}>
          Your intelligent travel companion powered by AI
        </Text>
      </View>

      <View style={[styles.statsContainer, isTablet && styles.statsContainerTablet]}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{trips.length}</Text>
          <Text style={styles.statLabel}>Active Trips</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>12</Text>
          <Text style={styles.statLabel}>Countries</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>48</Text>
          <Text style={styles.statLabel}>Cities</Text>
        </View>
      </View>

      <View style={[styles.card, isTablet && styles.cardTablet]}>
        <Text style={styles.cardTitle}>✨ Premium Features</Text>
        <View style={styles.featureGrid}>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🤖</Text>
            <Text style={styles.featureText}>AI Planning</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🗺️</Text>
            <Text style={styles.featureText}>Smart Routes</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🏨</Text>
            <Text style={styles.featureText}>Hotel Booking</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>📸</Text>
            <Text style={styles.featureText}>Photo Guide</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>📄</Text>
            <Text style={styles.featureText}>PDF Export</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🌤️</Text>
            <Text style={styles.featureText}>Weather</Text>
          </View>
        </View>
      </View>

      <View style={[styles.buttonContainer, isTablet && styles.buttonContainerTablet]}>
        <TouchableOpacity
          style={[styles.primaryButton, isTablet && styles.primaryButtonTablet]}
          onPress={() => setCurrentScreen('create')}
        >
          <Text style={styles.buttonText}>🚀 Plan New Trip</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.secondaryButton, isTablet && styles.secondaryButtonTablet]}
          onPress={() => setCurrentScreen('trips')}
        >
          <Text style={styles.secondaryButtonText}>📋 View My Trips ({trips.length})</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statusCard}>
        <Text style={styles.statusText}>✅ All systems operational</Text>
        <Text style={styles.statusSubtext}>Ready for intelligent trip planning</Text>
      </View>
    </ScrollView>
  );

  const renderCreateTripScreen = () => (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')} style={styles.backButtonContainer}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={[styles.title, isTablet && styles.titleTablet]}>✈️ Create New Trip</Text>
        <Text style={styles.subtitle}>Let AI plan your perfect journey</Text>
      </View>

      <View style={[styles.form, isTablet && styles.formTablet]}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>📍 Destination</Text>
          <TextInput
            style={[styles.input, isTablet && styles.inputTablet]}
            placeholder="Where do you want to go? (e.g., Tokyo, Japan)"
            value={tripData.destination}
            onChangeText={(text) => setTripData({...tripData, destination: text})}
            placeholderTextColor="#9ca3af"
          />
        </View>

        <View style={[styles.dateContainer, isTablet && styles.dateContainerTablet]}>
          <View style={styles.dateInput}>
            <Text style={styles.label}>📅 Start Date</Text>
            <TextInput
              style={[styles.input, isTablet && styles.inputTablet]}
              placeholder="2024-12-15"
              value={tripData.startDate}
              onChangeText={(text) => setTripData({...tripData, startDate: text})}
              placeholderTextColor="#9ca3af"
            />
          </View>
          <View style={styles.dateInput}>
            <Text style={styles.label}>📅 End Date</Text>
            <TextInput
              style={[styles.input, isTablet && styles.inputTablet]}
              placeholder="2024-12-22"
              value={tripData.endDate}
              onChangeText={(text) => setTripData({...tripData, endDate: text})}
              placeholderTextColor="#9ca3af"
            />
          </View>
        </View>

        <View style={[styles.detailsContainer, isTablet && styles.detailsContainerTablet]}>
          <View style={styles.detailInput}>
            <Text style={styles.label}>👥 Travelers</Text>
            <TextInput
              style={[styles.input, isTablet && styles.inputTablet]}
              placeholder="1"
              value={tripData.travelers}
              onChangeText={(text) => setTripData({...tripData, travelers: text})}
              keyboardType="numeric"
              placeholderTextColor="#9ca3af"
            />
          </View>
          <View style={styles.detailInput}>
            <Text style={styles.label}>💰 Budget</Text>
            <TextInput
              style={[styles.input, isTablet && styles.inputTablet]}
              placeholder="$2,500"
              value={tripData.budget}
              onChangeText={(text) => setTripData({...tripData, budget: text})}
              placeholderTextColor="#9ca3af"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>🎯 Preferences (Optional)</Text>
          <TextInput
            style={[styles.input, styles.textArea, isTablet && styles.inputTablet]}
            placeholder="Adventure, culture, food, relaxation, nightlife..."
            value={tripData.preferences}
            onChangeText={(text) => setTripData({...tripData, preferences: text})}
            multiline={true}
            numberOfLines={3}
            placeholderTextColor="#9ca3af"
          />
        </View>

        <TouchableOpacity
          style={[
            styles.primaryButton,
            isTablet && styles.primaryButtonTablet,
            isLoading && styles.buttonDisabled
          ]}
          onPress={handleCreateTrip}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? '🤖 AI is planning your trip...' : '🤖 Generate AI Trip Plan'}
          </Text>
        </TouchableOpacity>

        {isLoading && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>✨ Analyzing destinations...</Text>
            <Text style={styles.loadingText}>🗺️ Optimizing routes...</Text>
            <Text style={styles.loadingText}>🏨 Finding best accommodations...</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );

  const renderTripsScreen = () => (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')} style={styles.backButtonContainer}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={[styles.title, isTablet && styles.titleTablet]}>📋 My Trips</Text>
        <Text style={styles.subtitle}>Manage your travel adventures</Text>
      </View>

      {trips.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>✈️</Text>
          <Text style={styles.emptyStateTitle}>No trips yet</Text>
          <Text style={styles.emptyStateText}>Start planning your first adventure!</Text>
        </View>
      ) : (
        <View style={[styles.tripsGrid, isTablet && styles.tripsGridTablet]}>
          {trips.map((trip) => (
            <View key={trip.id} style={[styles.tripCard, isTablet && styles.tripCardTablet]}>
              <View style={styles.tripHeader}>
                <Text style={styles.tripIcon}>{trip.image}</Text>
                <View style={[styles.statusBadge, trip.status === 'confirmed' ? styles.statusConfirmed : styles.statusPlanning]}>
                  <Text style={styles.statusText}>
                    {trip.status === 'confirmed' ? '✅ Confirmed' : '📝 Planning'}
                  </Text>
                </View>
              </View>

              <Text style={styles.tripTitle}>{trip.title}</Text>
              <Text style={styles.tripDestination}>{trip.destination}</Text>

              <View style={styles.tripDetails}>
                <View style={styles.tripDetailItem}>
                  <Text style={styles.tripDetailIcon}>📅</Text>
                  <Text style={styles.tripDetailText}>
                    {new Date(trip.startDate).toLocaleDateString()} - {new Date(trip.endDate).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.tripDetailItem}>
                  <Text style={styles.tripDetailIcon}>👥</Text>
                  <Text style={styles.tripDetailText}>{trip.travelers} traveler{trip.travelers > 1 ? 's' : ''}</Text>
                </View>
                <View style={styles.tripDetailItem}>
                  <Text style={styles.tripDetailIcon}>💰</Text>
                  <Text style={styles.tripDetailText}>{trip.budget}</Text>
                </View>
              </View>

              <View style={styles.tripActions}>
                <TouchableOpacity style={styles.actionButton}>
                  <Text style={styles.actionButtonText}>📋 View Details</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButtonSecondary}>
                  <Text style={styles.actionButtonSecondaryText}>✏️ Edit</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      )}

      <TouchableOpacity
        style={[styles.primaryButton, isTablet && styles.primaryButtonTablet]}
        onPress={() => setCurrentScreen('create')}
      >
        <Text style={styles.buttonText}>➕ Create New Trip</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderBottomNav = () => (
    <View style={styles.bottomNav}>
      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'home' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('home')}
      >
        <Text style={[styles.navText, currentScreen === 'home' && styles.activeNavText]}>
          🏠 Home
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'create' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('create')}
      >
        <Text style={[styles.navText, currentScreen === 'create' && styles.activeNavText]}>
          ➕ Create
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'trips' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('trips')}
      >
        <Text style={[styles.navText, currentScreen === 'trips' && styles.activeNavText]}>
          📋 Trips
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.wrapper}>
      <StatusBar barStyle="dark-content" backgroundColor="#f8fafc" />
      <View style={styles.content}>
        {currentScreen === 'home' && renderHomeScreen()}
        {currentScreen === 'create' && renderCreateTripScreen()}
        {currentScreen === 'trips' && renderTripsScreen()}
      </View>
      {renderBottomNav()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    padding: isWeb ? 40 : 20,
    paddingTop: isWeb ? 40 : 20,
    maxWidth: isWeb ? 1200 : '100%',
    alignSelf: 'center',
    width: '100%',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    paddingHorizontal: isTablet ? 40 : 0,
  },
  title: {
    fontSize: isTablet ? 36 : 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  titleTablet: {
    fontSize: 42,
  },
  subtitle: {
    fontSize: isTablet ? 18 : 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  subtitleTablet: {
    fontSize: 20,
    lineHeight: 28,
  },
  backButtonContainer: {
    alignSelf: 'flex-start',
    marginBottom: 20,
  },
  backButton: {
    fontSize: 18,
    color: '#6366f1',
    fontWeight: '600',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  // Stats Container
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
    paddingHorizontal: isTablet ? 20 : 0,
  },
  statsContainerTablet: {
    paddingHorizontal: 40,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },

  // Cards
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTablet: {
    padding: 32,
    marginHorizontal: isTablet ? 20 : 0,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },

  // Feature Grid
  featureGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 20,
  },
  featureIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 12,
    color: '#475569',
    fontWeight: '500',
    textAlign: 'center',
  },
  // Buttons
  buttonContainer: {
    paddingHorizontal: isTablet ? 20 : 0,
  },
  buttonContainerTablet: {
    paddingHorizontal: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  primaryButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  primaryButtonTablet: {
    paddingVertical: 20,
    paddingHorizontal: 40,
    flex: isTablet ? 0.48 : 1,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#6366f1',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
    marginBottom: 20,
  },
  secondaryButtonTablet: {
    paddingVertical: 20,
    paddingHorizontal: 40,
    flex: isTablet ? 0.48 : 1,
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
    shadowOpacity: 0.1,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  secondaryButtonText: {
    color: '#6366f1',
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },

  // Form Styles
  form: {
    paddingHorizontal: isTablet ? 20 : 0,
  },
  formTablet: {
    paddingHorizontal: 40,
    maxWidth: 800,
    alignSelf: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  dateContainerTablet: {
    marginBottom: 24,
  },
  dateInput: {
    flex: 0.48,
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  detailsContainerTablet: {
    marginBottom: 24,
  },
  detailInput: {
    flex: 0.48,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#1e293b',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputTablet: {
    paddingVertical: 18,
    paddingHorizontal: 20,
    fontSize: 18,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  loadingContainer: {
    backgroundColor: '#ede9fe',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  loadingText: {
    color: '#6366f1',
    fontSize: 14,
    marginBottom: 4,
    textAlign: 'center',
  },
  // Trip Cards
  tripsGrid: {
    marginBottom: 20,
  },
  tripsGridTablet: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  tripCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  tripCardTablet: {
    width: '48%',
    marginHorizontal: 0,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tripIcon: {
    fontSize: 32,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusConfirmed: {
    backgroundColor: '#dcfce7',
  },
  statusPlanning: {
    backgroundColor: '#fef3c7',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  tripTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 4,
  },
  tripDestination: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  tripDetails: {
    marginBottom: 16,
  },
  tripDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripDetailIcon: {
    fontSize: 14,
    marginRight: 8,
    width: 20,
  },
  tripDetailText: {
    fontSize: 14,
    color: '#475569',
    flex: 1,
  },
  tripActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 0.48,
  },
  actionButtonSecondary: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#6366f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 0.48,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  actionButtonSecondaryText: {
    color: '#6366f1',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },

  // Status Card
  statusCard: {
    backgroundColor: '#dcfce7',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e',
    marginTop: 20,
    marginHorizontal: isTablet ? 20 : 0,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 4,
    textAlign: 'center',
  },
  statusSubtext: {
    fontSize: 14,
    color: '#15803d',
    textAlign: 'center',
  },
  // Bottom Navigation
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxWidth: isWeb ? 1200 : '100%',
    alignSelf: 'center',
    width: '100%',
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  activeNavItem: {
    backgroundColor: '#ede9fe',
  },
  navText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  activeNavText: {
    color: '#6366f1',
  },
});
