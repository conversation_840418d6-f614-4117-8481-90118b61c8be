import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🌍 AI Trip Planner</Text>
      <Text style={styles.subtitle}>Mobile App Running Successfully!</Text>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>✨ Features</Text>
        <Text style={styles.feature}>🤖 AI-powered trip planning</Text>
        <Text style={styles.feature}>🗺️ Interactive maps & routes</Text>
        <Text style={styles.feature}>🏨 Hotel booking</Text>
        <Text style={styles.feature}>📸 Photo integration</Text>
        <Text style={styles.feature}>📄 PDF export</Text>
      </View>

      <View style={styles.statusCard}>
        <Text style={styles.statusText}>✅ App is working perfectly!</Text>
        <Text style={styles.statusSubtext}>Ready for development</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 40,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  feature: {
    fontSize: 16,
    color: '#475569',
    marginBottom: 8,
    textAlign: 'center',
  },
  statusCard: {
    backgroundColor: '#dcfce7',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    maxWidth: 400,
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 4,
    textAlign: 'center',
  },
  statusSubtext: {
    fontSize: 14,
    color: '#15803d',
    textAlign: 'center',
  },
});
