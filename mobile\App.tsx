import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, TextInput, Alert } from 'react-native';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState('home');
  const [tripData, setTripData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    travelers: '1',
    budget: ''
  });

  const handleCreateTrip = () => {
    if (!tripData.destination) {
      Alert.alert('Error', 'Please enter a destination');
      return;
    }
    Alert.alert(
      'Success! 🎉',
      `AI Trip Plan for ${tripData.destination} created successfully!\n\n✅ Itinerary generated\n✅ Hotels found\n✅ Activities planned\n✅ Routes optimized`,
      [
        { text: 'View Trip', onPress: () => setCurrentScreen('trips') },
        { text: 'Create Another', onPress: () => {
          setTripData({ destination: '', startDate: '', endDate: '', travelers: '1', budget: '' });
        }},
        { text: 'Go Home', onPress: () => setCurrentScreen('home') }
      ]
    );
  };

  const renderHomeScreen = () => (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🌍 AI Trip Planner</Text>
        <Text style={styles.subtitle}>Your intelligent travel companion</Text>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>✨ Features</Text>
        <Text style={styles.feature}>🤖 AI-powered trip planning</Text>
        <Text style={styles.feature}>🗺️ Interactive maps & routes</Text>
        <Text style={styles.feature}>🏨 Hotel booking</Text>
        <Text style={styles.feature}>📸 Photo integration</Text>
        <Text style={styles.feature}>📄 PDF export</Text>
      </View>

      <TouchableOpacity
        style={styles.primaryButton}
        onPress={() => setCurrentScreen('create')}
      >
        <Text style={styles.buttonText}>🚀 Plan New Trip</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.secondaryButton}
        onPress={() => setCurrentScreen('trips')}
      >
        <Text style={styles.secondaryButtonText}>📋 View My Trips</Text>
      </TouchableOpacity>

      <View style={styles.statusCard}>
        <Text style={styles.statusText}>✅ App is working perfectly!</Text>
        <Text style={styles.statusSubtext}>Ready for trip planning</Text>
      </View>
    </ScrollView>
  );

  const renderCreateTripScreen = () => (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>✈️ Create New Trip</Text>
      </View>

      <View style={styles.form}>
        <Text style={styles.label}>📍 Destination</Text>
        <TextInput
          style={styles.input}
          placeholder="Where do you want to go?"
          value={tripData.destination}
          onChangeText={(text) => setTripData({...tripData, destination: text})}
        />

        <Text style={styles.label}>📅 Start Date</Text>
        <TextInput
          style={styles.input}
          placeholder="YYYY-MM-DD"
          value={tripData.startDate}
          onChangeText={(text) => setTripData({...tripData, startDate: text})}
        />

        <Text style={styles.label}>📅 End Date</Text>
        <TextInput
          style={styles.input}
          placeholder="YYYY-MM-DD"
          value={tripData.endDate}
          onChangeText={(text) => setTripData({...tripData, endDate: text})}
        />

        <Text style={styles.label}>👥 Number of Travelers</Text>
        <TextInput
          style={styles.input}
          placeholder="1"
          value={tripData.travelers}
          onChangeText={(text) => setTripData({...tripData, travelers: text})}
          keyboardType="numeric"
        />

        <Text style={styles.label}>💰 Budget (Optional)</Text>
        <TextInput
          style={styles.input}
          placeholder="$1000"
          value={tripData.budget}
          onChangeText={(text) => setTripData({...tripData, budget: text})}
        />

        <TouchableOpacity style={styles.primaryButton} onPress={handleCreateTrip}>
          <Text style={styles.buttonText}>🤖 Generate AI Trip Plan</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderTripsScreen = () => (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>📋 My Trips</Text>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>🏖️ Sample Trip: Bali</Text>
        <Text style={styles.feature}>📅 Dec 15-22, 2024</Text>
        <Text style={styles.feature}>👥 2 travelers</Text>
        <Text style={styles.feature}>💰 $2,500 budget</Text>
        <TouchableOpacity style={styles.smallButton}>
          <Text style={styles.smallButtonText}>View Details</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>🗼 Sample Trip: Paris</Text>
        <Text style={styles.feature}>📅 Jan 10-17, 2025</Text>
        <Text style={styles.feature}>👥 1 traveler</Text>
        <Text style={styles.feature}>💰 $3,000 budget</Text>
        <TouchableOpacity style={styles.smallButton}>
          <Text style={styles.smallButtonText}>View Details</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.primaryButton}
        onPress={() => setCurrentScreen('create')}
      >
        <Text style={styles.buttonText}>➕ Create New Trip</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderBottomNav = () => (
    <View style={styles.bottomNav}>
      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'home' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('home')}
      >
        <Text style={[styles.navText, currentScreen === 'home' && styles.activeNavText]}>
          🏠 Home
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'create' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('create')}
      >
        <Text style={[styles.navText, currentScreen === 'create' && styles.activeNavText]}>
          ➕ Create
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.navItem, currentScreen === 'trips' && styles.activeNavItem]}
        onPress={() => setCurrentScreen('trips')}
      >
        <Text style={[styles.navText, currentScreen === 'trips' && styles.activeNavText]}>
          📋 Trips
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.wrapper}>
      <View style={styles.content}>
        {currentScreen === 'home' && renderHomeScreen()}
        {currentScreen === 'create' && renderCreateTripScreen()}
        {currentScreen === 'trips' && renderTripsScreen()}
      </View>
      {renderBottomNav()}
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    padding: 20,
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
  },
  backButton: {
    fontSize: 18,
    color: '#6366f1',
    marginBottom: 20,
    fontWeight: '600',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
  },
  feature: {
    fontSize: 15,
    color: '#475569',
    marginBottom: 6,
  },
  primaryButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#6366f1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#6366f1',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 20,
  },
  smallButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  secondaryButtonText: {
    color: '#6366f1',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  smallButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  statusCard: {
    backgroundColor: '#dcfce7',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e',
    marginTop: 20,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 4,
    textAlign: 'center',
  },
  statusSubtext: {
    fontSize: 14,
    color: '#15803d',
    textAlign: 'center',
  },
  form: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#1e293b',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  activeNavItem: {
    backgroundColor: '#ede9fe',
  },
  navText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  activeNavText: {
    color: '#6366f1',
  },
});
