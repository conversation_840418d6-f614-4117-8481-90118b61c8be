import React from 'react';
import { View, StyleSheet, Image, Dimensions } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthStackParamList } from '../../types';
import { theme, spacing } from '../../constants/theme';

const { width, height } = Dimensions.get('window');

type WelcomeScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Welcome'>;

interface Props {
  navigation: WelcomeScreenNavigationProp;
}

const WelcomeScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <LinearGradient
      colors={[theme.colors.primary, '#1565c0']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <Image
              source={{ uri: 'https://via.placeholder.com/120x120/ffffff/1976d2?text=AI' }}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={styles.title}>AI Trip Planner</Text>
            <Text style={styles.subtitle}>
              Plan your perfect trip with AI-powered recommendations
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Login')}
              style={[styles.button, styles.loginButton]}
              labelStyle={styles.loginButtonText}
            >
              Sign In
            </Button>
            
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Register')}
              style={[styles.button, styles.registerButton]}
              labelStyle={styles.registerButtonText}
            >
              Create Account
            </Button>
          </View>

          <View style={styles.features}>
            <Text style={styles.featuresTitle}>Why Choose AI Trip Planner?</Text>
            <View style={styles.featureItem}>
              <Text style={styles.featureText}>🤖 AI-powered itinerary generation</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureText}>🏨 Smart accommodation recommendations</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureText}>🗺️ Interactive maps and navigation</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureText}>☀️ Real-time weather forecasts</Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: height * 0.1,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
  },
  buttonContainer: {
    marginBottom: spacing.xl,
  },
  button: {
    marginBottom: spacing.md,
    borderRadius: 25,
    paddingVertical: spacing.xs,
  },
  loginButton: {
    backgroundColor: '#ffffff',
  },
  loginButtonText: {
    color: theme.colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  registerButton: {
    borderColor: '#ffffff',
    borderWidth: 2,
  },
  registerButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  features: {
    marginBottom: spacing.xl,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  featureItem: {
    marginBottom: spacing.sm,
  },
  featureText: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.9,
    textAlign: 'center',
  },
});

export default WelcomeScreen;
