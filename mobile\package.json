{"name": "ai-trip-planner-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~3.2.3", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "axios": "^1.6.8", "expo": "~51.0.28", "expo-linear-gradient": "~13.0.2", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-dom": "^19.1.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-paper": "^5.12.3", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "^0.19.13"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.79", "typescript": "~5.3.3"}, "private": true}