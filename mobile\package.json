{"name": "ai-trip-planner-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit": "eas submit", "test": "jest --watchAll", "lint": "expo lint", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "expo": "~51.0.28", "expo-camera": "~15.0.14", "expo-constants": "~16.0.2", "expo-font": "~12.0.9", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~13.0.2", "expo-location": "~17.0.1", "expo-maps": "~0.7.0", "expo-notifications": "~0.28.15", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-calendars": "^1.1302.0", "react-native-gesture-handler": "~2.16.1", "react-native-maps": "1.14.0", "react-native-paper": "^5.11.6", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~18.2.79", "@types/react-test-renderer": "^18.0.7", "jest": "^29.2.1", "jest-expo": "~51.0.3", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "jest": {"preset": "jest-expo"}, "private": true}