# AI Trip Planner Mobile App

A React Native mobile application for the AI Trip Planner platform, built with Expo and TypeScript.

## ✨ Features

- **Cross-Platform**: Runs on both iOS and Android
- **AI-Powered Trip Planning**: Generate personalized itineraries
- **Accommodation Booking**: Search and book hotels and apartments
- **Interactive Maps**: View trip locations and get directions
- **Weather Forecasts**: Check weather for destinations
- **User Authentication**: Secure login and registration
- **Offline Support**: Basic functionality works offline
- **Push Notifications**: Get updates about trips and bookings

## 🛠️ Tech Stack

- **React Native** with Expo SDK 51
- **TypeScript** for type safety
- **React Navigation 6** for navigation
- **React Native Paper** for Material Design UI
- **Expo Location** for GPS functionality
- **Expo Camera** for photo capture
- **AsyncStorage** for local data persistence
- **Axios** for API communication
- **React Native Reanimated** for animations
- **React Native Gesture Handler** for gestures

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

## 🚀 Quick Start

1. **Navigate to the mobile directory:**
```bash
cd mobile
```

2. **Install dependencies:**
```bash
npm install
```

3. **Copy environment variables:**
```bash
cp .env.example .env
```

4. **Start the development server:**
```bash
npm start
```

5. **Run on specific platform:**
```bash
# iOS Simulator
npm run ios

# Android Emulator
npm run android

# Web Browser
npm run web
```

## Project Structure

```
mobile/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   │   ├── auth/          # Authentication screens
│   │   └── main/          # Main app screens
│   ├── navigation/         # Navigation configuration
│   ├── contexts/          # React contexts
│   ├── services/          # API services
│   ├── types/             # TypeScript type definitions
│   ├── constants/         # App constants and themes
│   └── utils/             # Utility functions
├── assets/                # Images, fonts, etc.
├── App.tsx               # Main app component
├── app.json              # Expo configuration
├── package.json          # Dependencies
└── tsconfig.json         # TypeScript configuration
```

## Key Components

### Authentication
- Welcome screen with app introduction
- Login and registration forms
- Forgot password functionality
- Secure token storage

### Trip Management
- Trip creation and editing
- Trip list with search and filters
- Detailed trip view with itinerary
- Trip sharing capabilities

### Accommodations
- Search hotels and apartments
- View accommodation details
- Booking form with date selection
- Booking history

### Maps & Navigation
- Interactive maps with markers
- Route planning and directions
- Location-based recommendations
- Offline map support

### Profile & Settings
- User profile management
- Travel preferences
- App settings and preferences
- Help and support

## API Integration

The mobile app connects to the same backend API as the web application:

- **Base URL**: `http://localhost:3000/api` (development)
- **Authentication**: JWT tokens stored in AsyncStorage
- **Endpoints**: Same REST API endpoints as web app

## Environment Configuration

Create a `.env` file in the mobile directory:

```env
API_BASE_URL=http://localhost:3000/api
GOOGLE_MAPS_API_KEY=your_google_maps_key
WEATHER_API_KEY=your_weather_api_key
```

## Building for Production

### Android
```bash
npm run build:android
```

### iOS
```bash
npm run build:ios
```

## Testing

```bash
npm test
```

## Deployment

The app can be deployed using:
- **Expo Application Services (EAS)** for managed workflow
- **App Store Connect** for iOS
- **Google Play Console** for Android

## Contributing

1. Follow the existing code structure
2. Use TypeScript for all new files
3. Follow React Native best practices
4. Test on both iOS and Android
5. Update documentation as needed

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **iOS simulator not starting**: Reset simulator or restart Xcode
3. **Android emulator issues**: Check Android Studio AVD manager
4. **API connection issues**: Verify backend is running and accessible

### Performance Tips

- Use FlatList for large lists
- Implement proper image caching
- Use React.memo for expensive components
- Optimize bundle size with tree shaking

## License

MIT License - see LICENSE file for details
