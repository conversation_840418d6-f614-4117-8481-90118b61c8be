import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Trip, Accommodation, User, WeatherData, Attraction, Restaurant } from '../types';

// Base URL for the backend API
const BASE_URL = __DEV__ ? 'http://localhost:3000/api' : 'https://your-production-api.com/api';

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, redirect to login
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('user');
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (name: string, email: string, password: string) => {
    const response = await api.post('/auth/register', { name, email, password });
    return response.data;
  },

  logout: async () => {
    await AsyncStorage.removeItem('authToken');
    await AsyncStorage.removeItem('user');
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  updateProfile: async (userData: Partial<User>) => {
    const response = await api.put('/auth/profile', userData);
    return response.data;
  },
};

// Trip API
export const tripAPI = {
  generateTrip: async (tripData: any) => {
    const response = await api.post('/trips/generate', tripData);
    return response.data;
  },

  getTrips: async (): Promise<Trip[]> => {
    const response = await api.get('/trips');
    return response.data;
  },

  getTripById: async (id: string): Promise<Trip> => {
    const response = await api.get(`/trips/${id}`);
    return response.data;
  },

  updateTrip: async (id: string, tripData: Partial<Trip>) => {
    const response = await api.put(`/trips/${id}`, tripData);
    return response.data;
  },

  deleteTrip: async (id: string) => {
    const response = await api.delete(`/trips/${id}`);
    return response.data;
  },
};

// Accommodation API
export const accommodationAPI = {
  search: async (params: {
    location: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    type?: string;
  }): Promise<Accommodation[]> => {
    const response = await api.get('/accommodations/search', { params });
    return response.data;
  },

  getById: async (id: string): Promise<Accommodation> => {
    const response = await api.get(`/accommodations/${id}`);
    return response.data;
  },

  book: async (bookingData: {
    accommodationId: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    guestInfo: any;
  }) => {
    const response = await api.post('/accommodations/book', bookingData);
    return response.data;
  },
};

// Weather API
export const weatherAPI = {
  getForecast: async (location: string): Promise<WeatherData> => {
    const response = await api.get(`/weather/${encodeURIComponent(location)}`);
    return response.data;
  },
};

// Recommendations API
export const recommendationsAPI = {
  getRecommendations: async (params: {
    location: string;
    interests?: string;
    budget?: string;
  }): Promise<{ attractions: Attraction[]; restaurants: Restaurant[] }> => {
    const response = await api.get('/recommendations', { params });
    return response.data;
  },
};

// Map API
export const mapAPI = {
  getDirections: async (origin: string, destination: string, waypoints?: string[]) => {
    const response = await api.post('/map/directions', {
      origin,
      destination,
      waypoints,
    });
    return response.data;
  },

  searchPlaces: async (query: string, location?: string) => {
    const response = await api.get('/map/places', {
      params: { query, location },
    });
    return response.data;
  },
};

export default api;
