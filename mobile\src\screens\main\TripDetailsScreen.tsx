import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Button,
  Appbar,
  Chip,
  ActivityIndicator,
  FAB,
  List,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTrip } from '../../contexts/TripContext';
import { theme, spacing } from '../../constants/theme';
import { Trip, Itinerary } from '../../types';

interface Props {
  navigation: any;
  route: {
    params: {
      tripId: string;
    };
  };
}

export default function TripDetailsScreen({ navigation, route }: Props) {
  const { tripId } = route.params;
  const { trips, loading, fetchTripDetails } = useTrip();
  const [trip, setTrip] = useState<Trip | null>(null);
  const [itinerary, setItinerary] = useState<Itinerary[]>([]);

  useEffect(() => {
    const foundTrip = trips.find(t => t.id === tripId);
    if (foundTrip) {
      setTrip(foundTrip);
      if (foundTrip.itinerary) {
        setItinerary(foundTrip.itinerary);
      }
    } else {
      // Fetch trip details if not in local state
      fetchTripDetails(tripId).then(tripData => {
        setTrip(tripData);
        if (tripData.itinerary) {
          setItinerary(tripData.itinerary);
        }
      });
    }
  }, [tripId, trips]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusColor = () => {
    if (!trip) return theme.colors.surfaceVariant;

    const now = new Date();
    const start = new Date(trip.startDate);
    const end = new Date(trip.endDate);

    if (start > now) return theme.colors.primaryContainer;
    if (end < now) return theme.colors.surfaceVariant;
    return theme.colors.secondaryContainer;
  };

  const getStatusText = () => {
    if (!trip) return 'Unknown';

    const now = new Date();
    const start = new Date(trip.startDate);
    const end = new Date(trip.endDate);

    if (start > now) return 'Upcoming';
    if (end < now) return 'Completed';
    return 'Active';
  };

  const calculateDuration = () => {
    if (!trip) return 0;
    const start = new Date(trip.startDate);
    const end = new Date(trip.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const renderItineraryDay = (day: Itinerary, index: number) => (
    <Card key={index} style={styles.dayCard}>
      <Card.Content>
        <Text style={styles.dayTitle}>Day {day.day}</Text>
        <Text style={styles.dayDate}>{formatDate(day.date)}</Text>

        {day.activities.map((activity, actIndex) => (
          <View key={actIndex} style={styles.activityContainer}>
            <View style={styles.activityHeader}>
              <Text style={styles.activityTime}>
                {formatTime(activity.time)}
              </Text>
              <Chip
                mode="outlined"
                compact
                style={styles.activityType}
              >
                {activity.type}
              </Chip>
            </View>

            <Text style={styles.activityTitle}>{activity.title}</Text>

            {activity.description && (
              <Text style={styles.activityDescription}>
                {activity.description}
              </Text>
            )}

            {activity.location && (
              <Text style={styles.activityLocation}>
                📍 {activity.location}
              </Text>
            )}

            {activity.duration && (
              <Text style={styles.activityDuration}>
                ⏱️ {activity.duration}
              </Text>
            )}

            {actIndex < day.activities.length - 1 && (
              <Divider style={styles.activityDivider} />
            )}
          </View>
        ))}
      </Card.Content>
    </Card>
  );

  if (loading || !trip) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading trip details...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={trip.destination} />
        <Appbar.Action
          icon="pencil"
          onPress={() => navigation.navigate('EditTrip', { tripId })}
        />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Trip Overview */}
        <Card style={styles.overviewCard}>
          <Card.Content>
            <View style={styles.tripHeader}>
              <Text style={styles.tripTitle}>{trip.destination}</Text>
              <Chip
                mode="flat"
                style={[styles.statusChip, { backgroundColor: getStatusColor() }]}
              >
                {getStatusText()}
              </Chip>
            </View>

            <Text style={styles.tripDates}>
              {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
            </Text>

            <View style={styles.tripStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{calculateDuration()}</Text>
                <Text style={styles.statLabel}>Days</Text>
              </View>

              <View style={styles.statItem}>
                <Text style={styles.statValue}>{trip.travelers}</Text>
                <Text style={styles.statLabel}>Travelers</Text>
              </View>

              {trip.budget && (
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>${trip.budget}</Text>
                  <Text style={styles.statLabel}>Budget</Text>
                </View>
              )}
            </View>

            {trip.description && (
              <Text style={styles.tripDescription}>{trip.description}</Text>
            )}

            {trip.interests && trip.interests.length > 0 && (
              <View style={styles.interestsContainer}>
                <Text style={styles.interestsTitle}>Interests:</Text>
                <View style={styles.interestsChips}>
                  {trip.interests.map((interest, index) => (
                    <Chip key={index} mode="outlined" compact style={styles.interestChip}>
                      {interest}
                    </Chip>
                  ))}
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Itinerary */}
        {itinerary.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Itinerary</Text>
            {itinerary.map(renderItineraryDay)}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              icon="map"
              style={styles.actionButton}
              onPress={() => navigation.navigate('TripMap', { tripId })}
            >
              View Map
            </Button>

            <Button
              mode="outlined"
              icon="weather-partly-cloudy"
              style={styles.actionButton}
              onPress={() => navigation.navigate('Weather', { destination: trip.destination })}
            >
              Weather
            </Button>

            <Button
              mode="outlined"
              icon="file-pdf-box"
              style={styles.actionButton}
              onPress={() => {/* Export to PDF */}}
            >
              Export PDF
            </Button>
          </View>
        </View>
      </ScrollView>

      <FAB
        icon="pencil"
        style={styles.fab}
        onPress={() => navigation.navigate('EditTrip', { tripId })}
        label="Edit Trip"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  overviewCard: {
    marginBottom: spacing.lg,
    elevation: 2,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  tripTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    flex: 1,
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  tripDates: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    marginBottom: spacing.lg,
  },
  tripStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: spacing.xs,
  },
  tripDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: spacing.md,
  },
  interestsContainer: {
    marginTop: spacing.md,
  },
  interestsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: spacing.sm,
  },
  interestsChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  interestChip: {
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: theme.colors.primary,
  },
  dayCard: {
    marginBottom: spacing.md,
    elevation: 1,
  },
  dayTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  dayDate: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: spacing.md,
  },
  activityContainer: {
    marginBottom: spacing.md,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  activityTime: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
  },
  activityType: {
    height: 24,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  activityDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: spacing.xs,
    color: theme.colors.onSurfaceVariant,
  },
  activityLocation: {
    fontSize: 12,
    marginBottom: spacing.xs,
    color: theme.colors.onSurfaceVariant,
  },
  activityDuration: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  activityDivider: {
    marginTop: spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
    minWidth: 100,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});
