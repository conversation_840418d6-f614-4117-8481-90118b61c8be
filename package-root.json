{"name": "ai-trip-planner", "version": "1.0.0", "description": "AI-powered trip planning application with web and mobile interfaces", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:mobile": "cd mobile && npm start", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:mobile\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:mobile": "cd mobile && npm run build", "start": "cd backend && npm start", "start:mobile": "cd mobile && npm start", "android": "cd mobile && npm run android", "ios": "cd mobile && npm run ios", "test": "npm run test:frontend && npm run test:backend && npm run test:mobile", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:mobile": "cd mobile && npm test", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../mobile && npm install", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:mobile", "clean:frontend": "cd frontend && rm -rf node_modules && rm -rf dist", "clean:backend": "cd backend && rm -rf node_modules && rm -rf dist", "clean:mobile": "cd mobile && rm -rf node_modules && rm -rf .expo"}, "keywords": ["ai", "travel", "trip-planner", "react", "react-native", "node", "typescript", "expo"], "author": "AI Trip Planner Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "mobile"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-trip-planner.git"}, "bugs": {"url": "https://github.com/your-username/ai-trip-planner/issues"}, "homepage": "https://github.com/your-username/ai-trip-planner#readme"}