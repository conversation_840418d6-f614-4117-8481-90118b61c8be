# AI Trip Planner - Complete Cross-Platform Solution

A comprehensive AI-powered trip planning application with web and mobile interfaces, featuring intelligent itinerary generation, accommodation booking, and travel recommendations.

## 🌟 Features

### Core Features
- **AI-Powered Trip Planning**: Generate personalized itineraries using advanced AI
- **Smart Accommodation Booking**: Search and book hotels and apartments
- **Interactive Maps**: View trip locations with routes and directions
- **Weather Forecasts**: Real-time weather data for destinations
- **Travel Recommendations**: Personalized attraction and restaurant suggestions
- **PDF Export**: Download trip itineraries as formatted PDFs
- **Trip Sharing**: Share trips via social media and direct links

### Platform-Specific Features

#### Web Application
- Responsive design for desktop and tablet
- Advanced filtering and sorting
- Detailed accommodation galleries
- Interactive map integration
- PDF generation and download

#### Mobile Application
- Native iOS and Android experience
- GPS location services
- Camera integration for travel photos
- Push notifications
- Offline functionality
- Touch-optimized interface

## 🏗️ Architecture

```
ai-trip-planner/
├── frontend/          # React web application
├── backend/           # Node.js API server
├── mobile/           # React Native mobile app
└── shared/           # Shared utilities and types
```

### Technology Stack

#### Frontend (Web)
- **React 19** with TypeScript
- **Material-UI (MUI)** for components
- **React Router** for navigation
- **Axios** for API communication
- **Vite** for build tooling

#### Backend (API)
- **Node.js** with Express
- **TypeScript** for type safety
- **MongoDB** with Mongoose
- **JWT** for authentication
- **OpenAI API** for AI features
- **Supabase** for additional services

#### Mobile (React Native)
- **React Native** with Expo
- **TypeScript** for type safety
- **React Navigation** for navigation
- **React Native Paper** for UI
- **Expo Location** for GPS
- **Expo Maps** for mapping

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- MongoDB (local or cloud)
- Expo CLI (for mobile development)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/ai-trip-planner.git
cd ai-trip-planner
```

2. **Install all dependencies**
```bash
npm run install:all
```

3. **Set up environment variables**

Create `.env` files in each directory:

**Backend (.env)**
```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ai-trip-planner
JWT_SECRET=your-jwt-secret
OPENAI_API_KEY=your-openai-key
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-key
```

**Frontend (.env)**
```env
VITE_API_URL=http://localhost:3000/api
VITE_MAPBOX_TOKEN=your-mapbox-token
```

**Mobile (.env)**
```env
API_BASE_URL=http://localhost:3000/api
GOOGLE_MAPS_API_KEY=your-google-maps-key
```

4. **Start development servers**

```bash
# Start all services
npm run dev:all

# Or start individually
npm run dev:backend    # Backend API (port 3000)
npm run dev:frontend   # Web app (port 5173)
npm run dev:mobile     # Mobile app (Expo)
```

## 📱 Platform Access

### Web Application
- **Development**: http://localhost:5173
- **Production**: https://your-domain.com

### Mobile Application
- **iOS**: Use Expo Go app or iOS Simulator
- **Android**: Use Expo Go app or Android Emulator
- **Development**: Scan QR code from Expo CLI

## 🔧 Development

### Project Structure

#### Frontend
```
frontend/
├── src/
│   ├── components/     # Reusable UI components
│   ├── pages/         # Page components
│   ├── services/      # API services
│   ├── contexts/      # React contexts
│   └── utils/         # Utility functions
├── public/            # Static assets
└── package.json       # Dependencies
```

#### Backend
```
backend/
├── src/
│   ├── controllers/   # Route controllers
│   ├── models/        # Database models
│   ├── routes/        # API routes
│   ├── middleware/    # Custom middleware
│   └── services/      # Business logic
├── tests/             # Test files
└── package.json       # Dependencies
```

#### Mobile
```
mobile/
├── src/
│   ├── screens/       # Screen components
│   ├── navigation/    # Navigation setup
│   ├── components/    # Reusable components
│   ├── services/      # API services
│   └── contexts/      # React contexts
├── assets/            # Images and fonts
└── package.json       # Dependencies
```

### Available Scripts

```bash
# Development
npm run dev:all        # Start all services
npm run dev:backend    # Start backend only
npm run dev:frontend   # Start frontend only
npm run dev:mobile     # Start mobile only

# Building
npm run build          # Build web and backend
npm run build:frontend # Build web app
npm run build:backend  # Build backend
npm run build:mobile   # Build mobile app

# Testing
npm run test           # Run all tests
npm run test:frontend  # Test web app
npm run test:backend   # Test backend
npm run test:mobile    # Test mobile app

# Mobile specific
npm run android        # Run on Android
npm run ios           # Run on iOS

# Utilities
npm run install:all    # Install all dependencies
npm run clean         # Clean all node_modules
```

## 🌐 API Documentation

The backend provides a RESTful API with the following main endpoints:

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Trips
- `GET /api/trips` - Get user trips
- `POST /api/trips/generate` - Generate new trip
- `GET /api/trips/:id` - Get trip details
- `PUT /api/trips/:id` - Update trip
- `DELETE /api/trips/:id` - Delete trip

### Accommodations
- `GET /api/accommodations/search` - Search accommodations
- `GET /api/accommodations/:id` - Get accommodation details
- `POST /api/accommodations/book` - Book accommodation

### Recommendations
- `GET /api/recommendations` - Get travel recommendations

### Weather
- `GET /api/weather/:location` - Get weather forecast

## 🔐 Authentication

The application uses JWT (JSON Web Tokens) for authentication:

1. Users register/login through the frontend
2. Backend validates credentials and returns JWT
3. Frontend stores JWT in localStorage (web) or AsyncStorage (mobile)
4. JWT is included in API requests for protected routes

## 🗄️ Database Schema

### User Model
```typescript
{
  id: string;
  email: string;
  name: string;
  password: string; // hashed
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}
```

### Trip Model
```typescript
{
  id: string;
  userId: string;
  destination: string;
  startDate: Date;
  endDate: Date;
  duration: number;
  travelers: number;
  budget: string;
  plan: string; // AI-generated itinerary
  createdAt: Date;
  updatedAt: Date;
}
```

## 🚀 Deployment

### Web Application
- **Frontend**: Deploy to Vercel, Netlify, or AWS S3
- **Backend**: Deploy to Heroku, AWS EC2, or DigitalOcean

### Mobile Application
- **iOS**: Deploy to App Store via App Store Connect
- **Android**: Deploy to Google Play Store
- **Expo**: Use EAS Build for managed deployment

### Environment Setup
1. Set up production databases (MongoDB Atlas)
2. Configure environment variables for production
3. Set up CI/CD pipelines
4. Configure domain and SSL certificates

## 🧪 Testing

### Frontend Testing
- Unit tests with Jest and React Testing Library
- E2E tests with Cypress
- Component testing with Storybook

### Backend Testing
- Unit tests with Jest
- Integration tests with Supertest
- API testing with Postman/Newman

### Mobile Testing
- Unit tests with Jest
- Component testing with React Native Testing Library
- E2E testing with Detox

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write tests for new features
- Update documentation
- Follow existing code style
- Test on multiple platforms

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the README files in each directory
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: <EMAIL>

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Basic trip planning
- ✅ Accommodation booking
- ✅ Web and mobile apps
- ✅ User authentication

### Phase 2 (Next)
- [ ] Real-time collaboration
- [ ] Advanced AI features
- [ ] Social features
- [ ] Offline synchronization

### Phase 3 (Future)
- [ ] AR/VR integration
- [ ] Voice assistants
- [ ] IoT integration
- [ ] Advanced analytics

---

**Built with ❤️ by the AI Trip Planner Team**
