import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, Image, Dimensions } from 'react-native';
import { Text, Card, Button, Appbar, FAB } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../../contexts/AuthContext';
import { useTrip } from '../../contexts/TripContext';
import { theme, spacing } from '../../constants/theme';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const { user } = useAuth();
  const { trips, fetchTrips } = useTrip();
  const [recentTrips, setRecentTrips] = useState([]);

  useEffect(() => {
    fetchTrips();
  }, []);

  useEffect(() => {
    // Get the 3 most recent trips
    const recent = trips.slice(0, 3);
    setRecentTrips(recent);
  }, [trips]);

  const destinations = [
    {
      name: 'Paris',
      image: 'https://images.unsplash.com/photo-1502602898657-3e91760cbb34?q=80&w=400',
      description: 'City of Light',
    },
    {
      name: 'Tokyo',
      image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?q=80&w=400',
      description: 'Modern meets Traditional',
    },
    {
      name: 'New York',
      image: 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?q=80&w=400',
      description: 'The Big Apple',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Content 
          title={`Welcome, ${user?.name?.split(' ')[0] || 'Traveler'}!`}
          titleStyle={styles.headerTitle}
        />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <LinearGradient
          colors={[theme.colors.primary, '#1565c0']}
          style={styles.heroSection}
        >
          <Text style={styles.heroTitle}>Plan Your Next Adventure</Text>
          <Text style={styles.heroSubtitle}>
            Let AI create the perfect itinerary for your dream trip
          </Text>
          <Button
            mode="contained"
            onPress={() => {/* Navigate to trip planner */}}
            style={styles.heroButton}
            labelStyle={styles.heroButtonText}
          >
            Start Planning
          </Button>
        </LinearGradient>

        {/* Recent Trips */}
        {recentTrips.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Your Recent Trips</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {recentTrips.map((trip: any) => (
                <Card key={trip.id} style={styles.tripCard}>
                  <Card.Content>
                    <Text style={styles.tripDestination}>{trip.destination}</Text>
                    <Text style={styles.tripDuration}>{trip.duration} days</Text>
                    <Text style={styles.tripDate}>
                      {new Date(trip.startDate).toLocaleDateString()}
                    </Text>
                  </Card.Content>
                </Card>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Popular Destinations */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular Destinations</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {destinations.map((destination, index) => (
              <Card key={index} style={styles.destinationCard}>
                <Image source={{ uri: destination.image }} style={styles.destinationImage} />
                <Card.Content style={styles.destinationContent}>
                  <Text style={styles.destinationName}>{destination.name}</Text>
                  <Text style={styles.destinationDescription}>{destination.description}</Text>
                </Card.Content>
              </Card>
            ))}
          </ScrollView>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <Card style={styles.actionCard}>
              <Card.Content style={styles.actionContent}>
                <Text style={styles.actionIcon}>🏨</Text>
                <Text style={styles.actionTitle}>Find Hotels</Text>
                <Text style={styles.actionDescription}>Discover perfect accommodations</Text>
              </Card.Content>
            </Card>
            
            <Card style={styles.actionCard}>
              <Card.Content style={styles.actionContent}>
                <Text style={styles.actionIcon}>🗺️</Text>
                <Text style={styles.actionTitle}>Explore</Text>
                <Text style={styles.actionDescription}>Find attractions nearby</Text>
              </Card.Content>
            </Card>
          </View>
        </View>

        {/* Weather Widget */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Travel Weather</Text>
          <Card style={styles.weatherCard}>
            <Card.Content>
              <Text style={styles.weatherTitle}>Check weather for your destination</Text>
              <Button mode="outlined" style={styles.weatherButton}>
                Get Weather Forecast
              </Button>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => {/* Navigate to trip planner */}}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.primary,
  },
  headerTitle: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  heroSection: {
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  heroSubtitle: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: spacing.lg,
  },
  heroButton: {
    backgroundColor: '#ffffff',
    borderRadius: 25,
  },
  heroButtonText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: spacing.md,
    marginHorizontal: spacing.lg,
    color: theme.colors.text,
  },
  tripCard: {
    width: 200,
    marginLeft: spacing.lg,
    marginRight: spacing.sm,
  },
  tripDestination: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  tripDuration: {
    fontSize: 14,
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  tripDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  destinationCard: {
    width: 250,
    marginLeft: spacing.lg,
    marginRight: spacing.sm,
  },
  destinationImage: {
    width: '100%',
    height: 150,
  },
  destinationContent: {
    padding: spacing.md,
  },
  destinationName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  destinationDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
  },
  actionCard: {
    flex: 1,
  },
  actionContent: {
    alignItems: 'center',
    padding: spacing.lg,
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: spacing.sm,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  actionDescription: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
  },
  weatherCard: {
    marginHorizontal: spacing.lg,
  },
  weatherTitle: {
    fontSize: 16,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  weatherButton: {
    borderRadius: 20,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default HomeScreen;
