import Constants from 'expo-constants';

// Environment configuration
const ENV = {
  development: {
    apiUrl: 'http://localhost:3000/api',
    apiTimeout: 10000,
    enableLogging: true,
    enableAnalytics: false,
    enableCrashReporting: false,
  },
  staging: {
    apiUrl: 'https://staging-api.aitripplanner.com/api',
    apiTimeout: 15000,
    enableLogging: true,
    enableAnalytics: true,
    enableCrashReporting: true,
  },
  production: {
    apiUrl: 'https://api.aitripplanner.com/api',
    apiTimeout: 15000,
    enableLogging: false,
    enableAnalytics: true,
    enableCrashReporting: true,
  },
};

// Get current environment
const getEnvironment = () => {
  if (__DEV__) return 'development';

  const releaseChannel = Constants.expoConfig?.extra?.releaseChannel;
  if (releaseChannel === 'staging') return 'staging';

  return 'production';
};

const currentEnv = getEnvironment();

// Export configuration
export const Config = {
  ...ENV[currentEnv],
  environment: currentEnv,
  version: Constants.expoConfig?.version || '1.0.0',
  buildNumber: Constants.expoConfig?.ios?.buildNumber || '1',

  // API Configuration
  api: {
    baseUrl: ENV[currentEnv].apiUrl,
    timeout: ENV[currentEnv].apiTimeout,
    retryAttempts: 3,
    retryDelay: 1000,
  },

  // Feature flags
  features: {
    pushNotifications: true,
    analytics: ENV[currentEnv].enableAnalytics,
    crashReporting: ENV[currentEnv].enableCrashReporting,
    logging: ENV[currentEnv].enableLogging,
    offlineMode: true,
    biometricAuth: true,
    darkMode: true,
  },

  // App constants
  app: {
    name: 'AI Trip Planner',
    bundleId: 'com.aitripplanner.mobile',
    scheme: 'aitripplanner',
    storeUrl: {
      ios: 'https://apps.apple.com/app/ai-trip-planner/id123456789',
      android: 'https://play.google.com/store/apps/details?id=com.aitripplanner.mobile',
    },
  },

  // External services
  services: {
    googleMaps: {
      apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || '',
    },
    weather: {
      apiKey: process.env.EXPO_PUBLIC_WEATHER_API_KEY || '',
      baseUrl: 'https://api.openweathermap.org/data/2.5',
    },
    analytics: {
      apiKey: process.env.EXPO_PUBLIC_ANALYTICS_API_KEY || '',
    },
    sentry: {
      dsn: process.env.EXPO_PUBLIC_SENTRY_DSN || '',
    },
  },

  // UI Configuration
  ui: {
    theme: {
      primary: '#1976d2',
      secondary: '#dc004e',
      accent: '#03dac6',
      background: '#f5f5f5',
      surface: '#ffffff',
      error: '#f44336',
      success: '#4caf50',
      warning: '#ff9800',
      info: '#2196f3',
    },
    animation: {
      duration: 300,
      easing: 'ease-in-out',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    },
  },

  // Storage keys
  storage: {
    authToken: '@ai_trip_planner:auth_token',
    user: '@ai_trip_planner:user',
    preferences: '@ai_trip_planner:preferences',
    trips: '@ai_trip_planner:trips',
    offlineData: '@ai_trip_planner:offline_data',
  },

  // Validation rules
  validation: {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    password: {
      minLength: 6,
      requireUppercase: false,
      requireLowercase: false,
      requireNumbers: false,
      requireSpecialChars: false,
    },
    phone: /^\+?[\d\s\-\(\)]+$/,
  },

  // Limits and constraints
  limits: {
    maxTripDuration: 365, // days
    maxTravelers: 20,
    maxPhotosPerTrip: 50,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    cacheExpiry: 24 * 60 * 60 * 1000, // 24 hours
  },
};

export default Config;
