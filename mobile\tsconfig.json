{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/constants/*": ["./src/constants/*"], "@/contexts/*": ["./src/contexts/*"], "@/navigation/*": ["./src/navigation/*"], "@/hooks/*": ["./src/hooks/*"], "@/assets/*": ["./assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "dist", ".expo"]}