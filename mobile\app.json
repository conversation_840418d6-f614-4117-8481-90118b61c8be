{"expo": {"name": "AI Trip Planner", "slug": "ai-trip-planner", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.aitripplanner.mobile", "buildNumber": "1", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses location to provide personalized trip recommendations and navigation.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses location to provide personalized trip recommendations and navigation.", "NSCameraUsageDescription": "This app uses camera to capture travel photos and memories.", "NSPhotoLibraryUsageDescription": "This app accesses photo library to save and share travel photos.", "NSMicrophoneUsageDescription": "This app uses microphone for voice notes and travel recordings."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.aitripplanner.mobile", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-location", "expo-camera", "expo-notifications", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share travel memories.", "cameraPermission": "The app accesses your camera to let you take travel photos."}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "your-project-id"}}}}