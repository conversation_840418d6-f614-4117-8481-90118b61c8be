import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Trip } from '../types';
import { tripAPI } from '../services/api';

interface TripContextType {
  trips: Trip[];
  currentTrip: Trip | null;
  loading: boolean;
  fetchTrips: () => Promise<void>;
  fetchTripDetails: (id: string) => Promise<Trip>;
  createTrip: (tripData: any) => Promise<Trip>;
  updateTrip: (id: string, tripData: Partial<Trip>) => Promise<void>;
  deleteTrip: (id: string) => Promise<void>;
  setCurrentTrip: (trip: Trip | null) => void;
}

const TripContext = createContext<TripContextType | undefined>(undefined);

export const useTrip = () => {
  const context = useContext(TripContext);
  if (context === undefined) {
    throw new Error('useTrip must be used within a TripProvider');
  }
  return context;
};

interface TripProviderProps {
  children: ReactNode;
}

export const TripProvider: React.FC<TripProviderProps> = ({ children }) => {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [currentTrip, setCurrentTrip] = useState<Trip | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchTrips = async () => {
    try {
      setLoading(true);
      const fetchedTrips = await tripAPI.getTrips();
      setTrips(fetchedTrips);
    } catch (error) {
      console.error('Error fetching trips:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const fetchTripDetails = async (id: string): Promise<Trip> => {
    try {
      setLoading(true);
      const trip = await tripAPI.getTripById(id);
      // Update the trip in the local state if it exists
      setTrips(prevTrips =>
        prevTrips.map(t => t.id === id ? trip : t)
      );
      return trip;
    } catch (error) {
      console.error('Error fetching trip details:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const createTrip = async (tripData: any): Promise<Trip> => {
    try {
      setLoading(true);
      const newTrip = await tripAPI.generateTrip(tripData);
      setTrips(prevTrips => [newTrip, ...prevTrips]);
      return newTrip;
    } catch (error) {
      console.error('Error creating trip:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateTrip = async (id: string, tripData: Partial<Trip>) => {
    try {
      setLoading(true);
      const updatedTrip = await tripAPI.updateTrip(id, tripData);
      setTrips(prevTrips =>
        prevTrips.map(trip => trip.id === id ? updatedTrip : trip)
      );
      if (currentTrip?.id === id) {
        setCurrentTrip(updatedTrip);
      }
    } catch (error) {
      console.error('Error updating trip:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteTrip = async (id: string) => {
    try {
      setLoading(true);
      await tripAPI.deleteTrip(id);
      setTrips(prevTrips => prevTrips.filter(trip => trip.id !== id));
      if (currentTrip?.id === id) {
        setCurrentTrip(null);
      }
    } catch (error) {
      console.error('Error deleting trip:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value: TripContextType = {
    trips,
    currentTrip,
    loading,
    fetchTrips,
    fetchTripDetails,
    createTrip,
    updateTrip,
    deleteTrip,
    setCurrentTrip,
  };

  return (
    <TripContext.Provider value={value}>
      {children}
    </TripContext.Provider>
  );
};
