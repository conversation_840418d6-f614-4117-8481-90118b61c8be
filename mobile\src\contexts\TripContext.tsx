import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Trip } from '../types';
import { tripAPI } from '../services/api';

interface TripContextType {
  trips: Trip[];
  currentTrip: Trip | null;
  isLoading: boolean;
  fetchTrips: () => Promise<void>;
  createTrip: (tripData: any) => Promise<Trip>;
  updateTrip: (id: string, tripData: Partial<Trip>) => Promise<void>;
  deleteTrip: (id: string) => Promise<void>;
  setCurrentTrip: (trip: Trip | null) => void;
}

const TripContext = createContext<TripContextType | undefined>(undefined);

export const useTrip = () => {
  const context = useContext(TripContext);
  if (context === undefined) {
    throw new Error('useTrip must be used within a TripProvider');
  }
  return context;
};

interface TripProviderProps {
  children: ReactNode;
}

export const TripProvider: React.FC<TripProviderProps> = ({ children }) => {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [currentTrip, setCurrentTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchTrips = async () => {
    try {
      setIsLoading(true);
      const fetchedTrips = await tripAPI.getTrips();
      setTrips(fetchedTrips);
    } catch (error) {
      console.error('Error fetching trips:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const createTrip = async (tripData: any): Promise<Trip> => {
    try {
      setIsLoading(true);
      const newTrip = await tripAPI.generateTrip(tripData);
      setTrips(prevTrips => [newTrip, ...prevTrips]);
      return newTrip;
    } catch (error) {
      console.error('Error creating trip:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateTrip = async (id: string, tripData: Partial<Trip>) => {
    try {
      setIsLoading(true);
      const updatedTrip = await tripAPI.updateTrip(id, tripData);
      setTrips(prevTrips =>
        prevTrips.map(trip => trip.id === id ? updatedTrip : trip)
      );
      if (currentTrip?.id === id) {
        setCurrentTrip(updatedTrip);
      }
    } catch (error) {
      console.error('Error updating trip:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTrip = async (id: string) => {
    try {
      setIsLoading(true);
      await tripAPI.deleteTrip(id);
      setTrips(prevTrips => prevTrips.filter(trip => trip.id !== id));
      if (currentTrip?.id === id) {
        setCurrentTrip(null);
      }
    } catch (error) {
      console.error('Error deleting trip:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: TripContextType = {
    trips,
    currentTrip,
    isLoading,
    fetchTrips,
    createTrip,
    updateTrip,
    deleteTrip,
    setCurrentTrip,
  };

  return (
    <TripContext.Provider value={value}>
      {children}
    </TripContext.Provider>
  );
};
