const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Fonts
  'ttf',
  'otf',
  'woff',
  'woff2',
  // Images
  'svg',
  'png',
  'jpg',
  'jpeg',
  'gif',
  'webp',
  // Audio/Video
  'mp3',
  'mp4',
  'mov',
  'avi',
  // Documents
  'pdf',
  'doc',
  'docx'
);

// Add TypeScript and JavaScript extensions
config.resolver.sourceExts.push(
  'ts',
  'tsx',
  'js',
  'jsx',
  'json',
  'mjs'
);

// Configure path aliases to match tsconfig.json
config.resolver.alias = {
  '@': path.resolve(__dirname, 'src'),
  '@/components': path.resolve(__dirname, 'src/components'),
  '@/screens': path.resolve(__dirname, 'src/screens'),
  '@/services': path.resolve(__dirname, 'src/services'),
  '@/utils': path.resolve(__dirname, 'src/utils'),
  '@/types': path.resolve(__dirname, 'src/types'),
  '@/constants': path.resolve(__dirname, 'src/constants'),
  '@/contexts': path.resolve(__dirname, 'src/contexts'),
  '@/navigation': path.resolve(__dirname, 'src/navigation'),
  '@/hooks': path.resolve(__dirname, 'src/hooks'),
  '@/assets': path.resolve(__dirname, 'assets'),
};

// Enable symlinks for monorepo support
config.resolver.unstable_enableSymlinks = true;

// Transformer configuration
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
  unstable_allowRequireContext: true,
};

// Exclude SVG from asset extensions since we're using svg-transformer
config.resolver.assetExts = config.resolver.assetExts.filter(ext => ext !== 'svg');
config.resolver.sourceExts.push('svg');

module.exports = config;
