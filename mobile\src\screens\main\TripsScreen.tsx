import React, { useEffect } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { Text, Card, Button, Appbar, FAB } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTrip } from '../../contexts/TripContext';
import { Trip } from '../../types';
import { theme, spacing } from '../../constants/theme';

const TripsScreen: React.FC = () => {
  const { trips, fetchTrips, isLoading } = useTrip();

  useEffect(() => {
    fetchTrips();
  }, []);

  const renderTripItem = ({ item }: { item: Trip }) => (
    <Card style={styles.tripCard}>
      <Card.Content>
        <Text style={styles.destination}>{item.destination}</Text>
        <Text style={styles.duration}>{item.duration} days</Text>
        <Text style={styles.dates}>
          {new Date(item.startDate).toLocaleDateString()} - {new Date(item.endDate).toLocaleDateString()}
        </Text>
        <Text style={styles.travelers}>{item.travelers} travelers</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="outlined">View Details</Button>
        <Button mode="contained">Edit</Button>
      </Card.Actions>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Content title="My Trips" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      {trips.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>No trips yet</Text>
          <Text style={styles.emptySubtitle}>Start planning your first adventure!</Text>
          <Button mode="contained" style={styles.emptyButton}>
            Create Trip
          </Button>
        </View>
      ) : (
        <FlatList
          data={trips}
          renderItem={renderTripItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.list}
          showsVerticalScrollIndicator={false}
        />
      )}

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => {/* Navigate to trip planner */}}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.primary,
  },
  headerTitle: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  list: {
    padding: spacing.lg,
  },
  tripCard: {
    marginBottom: spacing.md,
  },
  destination: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  duration: {
    fontSize: 14,
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  dates: {
    fontSize: 14,
    marginBottom: spacing.xs,
  },
  travelers: {
    fontSize: 12,
    opacity: 0.7,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: spacing.lg,
    opacity: 0.7,
  },
  emptyButton: {
    borderRadius: 25,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default TripsScreen;
