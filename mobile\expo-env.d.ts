/// <reference types="expo/types" />

// Environment variables type definitions
declare module '@env' {
  export const API_BASE_URL: string;
  export const API_TIMEOUT: string;
  export const GOOGLE_MAPS_API_KEY: string;
  export const GOOGLE_PLACES_API_KEY: string;
  export const WEATHER_API_KEY: string;
  export const WEATHER_API_URL: string;
  export const ANALYTICS_API_KEY: string;
  export const SENTRY_DSN: string;
  export const ENABLE_PUSH_NOTIFICATIONS: string;
  export const ENABLE_ANALYTICS: string;
  export const ENABLE_CRASH_REPORTING: string;
  export const DEBUG_MODE: string;
  export const LOG_LEVEL: string;
}

// Global type augmentations
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      EXPO_PUBLIC_API_URL?: string;
    }
  }
}

// React Navigation type safety
declare global {
  namespace ReactNavigation {
    interface RootParamList {
      Auth: undefined;
      Main: undefined;
      TripDetails: { tripId: string };
      AccommodationDetails: { accommodationId: string };
      BookingForm: { accommodation: any };
      MapView: { points: any[] };
      Profile: undefined;
      Settings: undefined;
    }
  }
}

// Expo modules type extensions
declare module 'expo-constants' {
  interface AppManifest {
    extra?: {
      apiUrl?: string;
      environment?: string;
    };
  }
}

export {};
